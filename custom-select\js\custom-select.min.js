class CustomSelect {
    static instances = new Set; constructor(e, t = {}) { this.element = e, this.isMultiple = e.hasAttribute("multiple") || "multi" === e.dataset.selectType, this.options = { placeholder: this.isMultiple ? "Select options" : "Select an option", searchable: !1, selectAll: !0, maxHeight: "260px", ...t }, this.selectedValues = this.isMultiple ? [] : null, this.allOptions = [], this.isOpen = !1, this.focusedIndex = -1, CustomSelect.instances.add(this), this.init() } init() { this.parseOptions(), this.createDropdown(), this.attachEventListeners(), this.updateDisplay() } parseOptions() { if ("SELECT" === this.element.tagName) { let e = Array.from(this.element.querySelectorAll("option")); this.allOptions = e.map(e => ({ value: e.value || e.textContent.trim(), label: e.textContent.trim(), selected: e.selected })) } else this.element.dataset.options ? this.allOptions = JSON.parse(this.element.dataset.options) : this.allOptions = this.options.options || []; if (this.isMultiple) this.selectedValues = this.allOptions.filter(e => e.selected).map(e => e.value); else { let t = this.allOptions.find(e => e.selected); this.selectedValues = t ? t.value : null } } createDropdown() {
        this.element.style.display = "none", this.container = document.createElement("div"), this.container.className = `custom-select-container ${this.isMultiple ? "multi" : "single"}`, this.trigger = document.createElement("div"), this.trigger.className = "custom-select-trigger", this.trigger.setAttribute("role", "button"), this.trigger.setAttribute("aria-haspopup", "listbox"), this.trigger.setAttribute("tabindex", "0"), this.triggerText = document.createElement("span"), this.triggerText.className = "custom-select-text", this.arrow = document.createElement("span"), this.arrow.className = "custom-select-arrow", this.arrow.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
        <path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `, this.trigger.appendChild(this.triggerText), this.trigger.appendChild(this.arrow), this.dropdown = document.createElement("div"), this.dropdown.className = "custom-select-dropdown", this.dropdown.setAttribute("role", "listbox"), this.isMultiple && this.dropdown.setAttribute("aria-multiselectable", "true"), this.isMultiple && (this.chipsContainer = document.createElement("div"), this.chipsContainer.className = "custom-select-chips"), this.container.appendChild(this.trigger), this.container.appendChild(this.dropdown), this.isMultiple && this.container.appendChild(this.chipsContainer), this.element.parentNode.insertBefore(this.container, this.element.nextSibling), this.renderDropdown()
    } renderDropdown() {
        let e = ""; if (this.isMultiple && this.options.selectAll && this.allOptions.length > 1) {
            let t = this.selectedValues.length === this.allOptions.length; e += `
        <div class="custom-select-item custom-select-select-all" role="option" data-value="__select_all__" data-index="-1">
          <input type="checkbox" id="select-all-${this.element.id}" class="custom-select-checkbox" ${t ? "checked" : ""} tabindex="-1">
          <label for="select-all-${this.element.id}" class="custom-select-item-text">Select All</label>
        </div>
      `} this.allOptions.forEach((t, s) => {
                let i = this.isMultiple ? this.selectedValues.includes(t.value) : this.selectedValues === t.value; if (this.isMultiple) {
                    let l = `${this.element.id}-option-${s}`; e += `
          <div class="custom-select-item ${i ? "selected" : ""}" role="option" data-value="${t.value}" data-index="${s}">
            <input type="checkbox" id="${l}" class="custom-select-checkbox" ${i ? "checked" : ""} tabindex="-1">
            <label for="${l}" class="custom-select-item-text">${t.label}</label>
          </div>
        `} else e += `
          <div class="custom-select-item ${i ? "selected" : ""}"
               role="option"
               data-value="${t.value}"
               data-index="${s}"
               aria-selected="${i}">
            <span class="custom-select-item-text">${t.label}</span>
          </div>
        `}), this.dropdown.innerHTML = `<div class="custom-select-items-wrapper">${e}</div>`
    } attachEventListeners() { this.boundToggle = e => { e.stopPropagation(), this.toggle() }, this.boundHandleDropdownClick = e => { e.stopPropagation(); let t = e.target.closest(".custom-select-item"); t && this.handleItemClick(t) }, this.boundHandleKeyDown = e => { this.handleKeyDown(e) }, this.boundHandleDocumentClick = () => { this.close() }, this.boundHandleChipClick = e => { if (e.target.classList.contains("custom-select-chip-remove")) { let t = e.target.dataset.value; this.toggleOption(t, !1) } }, this.trigger.addEventListener("click", this.boundToggle), this.dropdown.addEventListener("click", this.boundHandleDropdownClick), this.trigger.addEventListener("keydown", this.boundHandleKeyDown), this.dropdown.addEventListener("keydown", this.boundHandleKeyDown), document.addEventListener("click", this.boundHandleDocumentClick), this.isMultiple && this.chipsContainer.addEventListener("click", this.boundHandleChipClick) } handleKeyDown(e) { switch (e.key) { case "Enter": case " ": if (e.preventDefault(), this.isOpen) { if (this.focusedIndex >= 0) { let t = this.dropdown.querySelectorAll(".custom-select-item"), s = t[this.focusedIndex]; s && (this.handleItemClick(s), this.isMultiple || this.close()) } else this.toggle() } else this.open(); break; case "ArrowDown": e.preventDefault(), this.isOpen ? this.focusNext() : this.open(); break; case "ArrowUp": e.preventDefault(), this.isOpen && this.focusPrevious(); break; case "Escape": e.preventDefault(), this.close(), this.trigger.focus(); break; case "Tab": this.isOpen && this.close() } } handleItemClick(e) { let t = e.dataset.value; if ("__select_all__" === t) this.toggleSelectAll(); else if (this.isMultiple) { let s = e.querySelector(".custom-select-checkbox"); this.toggleOption(t, !s.checked) } else this.selectOption(t), this.close() } toggleSelectAll() { let e = this.selectedValues.length === this.allOptions.length; e ? this.selectedValues = [] : this.selectedValues = this.allOptions.map(e => e.value), this.updateDisplay(), this.renderDropdown(), this.dispatchChangeEvent() } toggleOption(e, t) { t && !this.selectedValues.includes(e) ? this.selectedValues.push(e) : t || (this.selectedValues = this.selectedValues.filter(t => t !== e)), this.updateDisplay(), this.renderDropdown(), this.dispatchChangeEvent() } selectOption(e) { this.selectedValues = e, this.updateDisplay(), this.renderDropdown(), this.dispatchChangeEvent() } updateDisplay() { if (this.isMultiple) { let e = this.selectedValues.length; 0 === e ? (this.triggerText.textContent = this.options.placeholder, this.triggerText.classList.add("placeholder")) : (this.triggerText.textContent = `${e} selected`, this.triggerText.classList.remove("placeholder")), this.updateChips(), "SELECT" === this.element.tagName && Array.from(this.element.options).forEach(e => { e.selected = this.selectedValues.includes(e.value) }) } else { let t = this.allOptions.find(e => e.value === this.selectedValues); t ? (this.triggerText.textContent = t.label, this.triggerText.classList.remove("placeholder")) : (this.triggerText.textContent = this.options.placeholder, this.triggerText.classList.add("placeholder")), "SELECT" === this.element.tagName && (this.element.value = this.selectedValues || "", Array.from(this.element.options).forEach(e => { e.selected = e.value === this.selectedValues })) } } updateChips() {
        this.chipsContainer.innerHTML = "", this.selectedValues.forEach(e => {
            let t = this.allOptions.find(t => t.value === e); if (t) {
                let s = document.createElement("div"); s.className = "custom-select-chip", s.innerHTML = `
          ${t.label}
          <span class="custom-select-chip-remove" data-value="${e}">\xd7</span>
        `, this.chipsContainer.appendChild(s)
            }
        })
    } focusNext() { let e = this.dropdown.querySelectorAll(".custom-select-item"), t = e.length - 1; this.focusedIndex < t ? this.focusedIndex++ : this.focusedIndex = 0, this.updateFocus(), this.scrollToFocused() } focusPrevious() { let e = this.dropdown.querySelectorAll(".custom-select-item"), t = e.length - 1; this.focusedIndex > 0 ? this.focusedIndex-- : this.focusedIndex = t, this.updateFocus(), this.scrollToFocused() } updateFocus() { let e = this.dropdown.querySelectorAll(".custom-select-item"); e.forEach((e, t) => { e.classList.toggle("focused", t === this.focusedIndex) }) } scrollToFocused() { let e = this.dropdown.querySelectorAll(".custom-select-item"), t = e[this.focusedIndex]; if (t) { let s = this.dropdown.querySelector(".custom-select-items-wrapper"); if (s) { let i = s.getBoundingClientRect(), l = t.getBoundingClientRect(), n = l.top >= i.top && l.bottom <= i.bottom; if (!n) { let o = t.offsetTop, a = s.clientHeight, c = t.offsetHeight; l.top < i.top ? s.scrollTop = o - 20 : l.bottom > i.bottom && (s.scrollTop = o - a + c + 20) } } else t.scrollIntoView({ block: "nearest", behavior: "smooth" }) } } toggle() { this.isOpen ? this.close() : this.open() } open() { if (CustomSelect.closeAllExcept(this), this.isOpen = !0, this.dropdown.classList.add("open"), this.trigger.classList.add("open"), this.trigger.setAttribute("aria-expanded", "true"), this.isMultiple) { let e = this.allOptions.findIndex(e => this.selectedValues.includes(e.value)); this.focusedIndex = e >= 0 ? e : 0 } else { let t = this.allOptions.findIndex(e => e.value === this.selectedValues); this.focusedIndex = t >= 0 ? t : 0 } this.updateFocus(), setTimeout(() => { this.scrollToFocused() }, 10) } close() { this.isOpen = !1, this.dropdown.classList.remove("open"), this.trigger.classList.remove("open"), this.trigger.setAttribute("aria-expanded", "false"), this.focusedIndex = -1; let e = this.dropdown.querySelectorAll(".custom-select-item"); e.forEach(e => e.classList.remove("focused")) } dispatchChangeEvent() { if (this.isMultiple) { let e = new CustomEvent("change", { detail: { selectedValues: this.selectedValues, selectedOptions: this.allOptions.filter(e => this.selectedValues.includes(e.value)) } }); this.element.dispatchEvent(e) } else { let t = this.allOptions.find(e => e.value === this.selectedValues), s = new CustomEvent("change", { detail: { selectedValue: this.selectedValues, selectedOption: t } }); this.element.dispatchEvent(s) } } getSelectedValue() { return this.isMultiple, this.selectedValues } getSelectedValues() { return this.isMultiple ? this.selectedValues : [this.selectedValues].filter(Boolean) } setSelectedValue(e) { this.isMultiple ? this.selectedValues = Array.isArray(e) ? e : [e] : this.selectedValues = e, this.updateDisplay(), this.renderDropdown() } destroy() { CustomSelect.instances.delete(this), this.isOpen && this.close(); try { this.trigger && this.boundToggle && (this.trigger.removeEventListener("click", this.boundToggle), this.trigger.removeEventListener("keydown", this.boundHandleKeyDown)), this.dropdown && this.boundHandleDropdownClick && (this.dropdown.removeEventListener("click", this.boundHandleDropdownClick), this.dropdown.removeEventListener("keydown", this.boundHandleKeyDown)), this.chipsContainer && this.isMultiple && this.boundHandleChipClick && this.chipsContainer.removeEventListener("click", this.boundHandleChipClick), this.boundHandleDocumentClick && document.removeEventListener("click", this.boundHandleDocumentClick) } catch (e) { console.warn("CustomSelect: Error removing event listeners during destroy:", e) } try { this.container && this.container.parentNode && this.container.parentNode.removeChild(this.container) } catch (t) { console.warn("CustomSelect: Error removing DOM elements during destroy:", t) } try { this.element && document.contains(this.element) && (this.element.style.display = "") } catch (s) { console.warn("CustomSelect: Error restoring original element during destroy:", s) } this.element = null, this.container = null, this.trigger = null, this.dropdown = null, this.chipsContainer = null, this.allOptions = null, this.selectedValues = null, this.boundToggle = null, this.boundHandleDropdownClick = null, this.boundHandleKeyDown = null, this.boundHandleDocumentClick = null, this.boundHandleChipClick = null } static closeAllExcept(e = null) { CustomSelect.instances.forEach(t => { t !== e && t.isOpen && t.close() }) } static closeAll() { CustomSelect.closeAllExcept(null) } static cleanupInvalidInstances() { let e = []; return CustomSelect.instances.forEach(t => { t.isValidInstance() || e.push(t) }), e.forEach(e => { e.destroy() }), e.length } isValidInstance() { return this.element && document.contains(this.element) } static init(e, t = {}) { let s = document.querySelectorAll(e), i = []; return s.forEach(e => { i.push(new CustomSelect(e, t)) }), 1 === i.length ? i[0] : i } static autoInit() { let e = document.querySelectorAll("select[data-select-type]"), t = []; return e.forEach(e => { let s = e.dataset.placeholder; t.push(new CustomSelect(e, s ? { placeholder: s } : {})) }), t }
} "undefined" != typeof document && ("loading" === document.readyState ? document.addEventListener("DOMContentLoaded", () => CustomSelect.autoInit()) : CustomSelect.autoInit(), window.addEventListener("beforeunload", () => { CustomSelect.instances.forEach(e => { e.destroy() }) }), document.addEventListener("visibilitychange", () => { document.hidden && CustomSelect.closeAll() }), setInterval(() => { let e = CustomSelect.cleanupInvalidInstances(); e > 0 && console.log(`CustomSelect: Cleaned up ${e} invalid instances`) }, 3e4));